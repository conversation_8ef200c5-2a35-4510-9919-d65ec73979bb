import { getSupabaseClient } from "./supabase/client";
import { transformPrompt, transformPromptCard, transformCategory, transformTool, transformTag, transformProfile, transformCollection, transformPromptCardWithSaved } from "./transformers";
import { createTitleSlug } from "./utils/url-helpers"; // Import for slug generation
import type { Profile, Collection, PromptCard, Notification, Prompt, Category, Tool, Tag, Comment, AIModel, CreatePromptData } from "./types"; // Import all types
import { logPromptShortIdIssues } from "./utils/debug-helpers";
import { standardizeAndMatchAIModel } from "./services/ai-model-standardization";
import { quickHealthCheck } from "./utils/supabase-health-check";

// Get Supabase client with proper error handling
function getSupabaseClientSafe() {
  try {
    const client = getSupabaseClient();
    if (!client) {
      throw new Error("Supabase client is not initialized");
    }
    return client;
  } catch (error) {
    console.error("[getSupabaseClientSafe] Failed to get Supabase client:", error);
    throw new Error("Database connection failed - please check your configuration");
  }
}

// --- Prompts ---
export async function getPrompts({
  categorySlugs,
  toolSlugs,
  tagSlugs,
  aiModelSlugs,
  searchQuery,
  userId, // existing: for filtering by author
  currentUserId, // NEW: for determining saved status
  limit = 20,
  offset = 0,
  sortBy = "created_at", // Default sort
  sortOrder = "desc",
  signal, // NEW: AbortSignal for request cancellation
}: {
  categorySlugs?: string[];
  toolSlugs?: string[];
  tagSlugs?: string[];
  aiModelSlugs?: string[];
  searchQuery?: string;
  userId?: string; // author filter
  currentUserId?: string; // NEW: for saved status
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  signal?: AbortSignal; // NEW: AbortSignal parameter
}): Promise<PromptCard[]> {
  // Generate unique call ID for tracking
  const callId = Math.random().toString(36).substring(2, 8);

  console.log(`[getPrompts:${callId}] Entry. Params:`, {
    categorySlugs: categorySlugs?.length || 0,
    toolSlugs: toolSlugs?.length || 0,
    tagSlugs: tagSlugs?.length || 0,
    aiModelSlugs: aiModelSlugs?.length || 0,
    searchQuery: searchQuery ? `"${searchQuery.substring(0, 50)}..."` : null,
    userId: userId ? `${userId.substring(0, 8)}...` : null,
    currentUserId: currentUserId ? `${currentUserId.substring(0, 8)}...` : null,
    limit,
    offset,
    sortBy,
    sortOrder,
    hasSignal: !!signal,
    signalAborted: signal?.aborted || false
  });

  try {
    console.log(`[getPrompts:${callId}] About to call quickHealthCheck`);
    // Perform a quick health check before proceeding
    const isHealthy = await quickHealthCheck();
    if (!isHealthy) {
      console.warn(`[getPrompts:${callId}] Supabase health check failed, but continuing with request`);
    }

    console.log(`[getPrompts:${callId}] About to get Supabase client`);
    // Get Supabase client with proper error handling
    const supabase = getSupabaseClientSafe();
    console.log(`[getPrompts:${callId}] Supabase client obtained successfully`);

    console.log("[getPrompts] Env vars check:", {
      url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      key: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      nodeEnv: process.env.NODE_ENV
    });

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error("[getPrompts] Missing Supabase environment variables");
      throw new Error("Database configuration is missing");
    }

    console.log("[getPrompts] About to test database connection and view accessibility");
    // First, let's test if the view exists and is accessible
    try {
      console.log("[getPrompts] Executing database view test query...");
      const testQuery = await supabase
        .from("prompt_card_details")
        .select("id")
        .limit(1);

      console.log("[getPrompts] Database view test query executed, checking result...");

      if (testQuery.error) {
        console.error("[getPrompts] Database view test failed:", {
          message: testQuery.error.message || 'No error message',
          details: testQuery.error.details || 'No error details',
          hint: testQuery.error.hint || 'No error hint',
          code: testQuery.error.code || 'No error code',
          errorType: typeof testQuery.error,
          errorKeys: testQuery.error ? Object.keys(testQuery.error) : [],
          errorStringified: testQuery.error ? JSON.stringify(testQuery.error) : 'null',
          fullError: testQuery.error
        });
        // If the view doesn't exist, fall back to the prompts table
        throw new Error(`Database view not accessible: ${testQuery.error.message || 'Unknown database error'}`);
      }
      console.log("[getPrompts] Database view test successful, data length:", testQuery.data?.length || 0);
    } catch (testError) {
      console.error("[getPrompts] Database view test threw exception:", {
        error: testError,
        message: testError instanceof Error ? testError.message : 'Unknown test error',
        errorType: typeof testError,
        errorKeys: testError && typeof testError === 'object' ? Object.keys(testError) : [],
        errorStringified: testError ? JSON.stringify(testError) : 'null'
      });
      // Return empty array if database is not accessible
      return [];
    }

    // Use the new RPC function when currentUserId is provided
    if (currentUserId) {
      try {
        console.log("[getPrompts] About to use RPC function with currentUserId:", {
          currentUserId: currentUserId.substring(0, 8) + '...',
          hasSignal: !!signal,
          signalAborted: signal?.aborted || false
        });

        // Validate that currentUserId is a valid UUID format
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(currentUserId)) {
          console.warn("[getPrompts] Invalid UUID format for currentUserId, falling back to standard query:", currentUserId);
          throw new Error("Invalid UUID format for currentUserId");
        }

        console.log("[getPrompts] UUID validation passed, preparing RPC parameters");

        // Convert empty arrays to null (RPC function doesn't handle empty arrays correctly)
        const normalizeArray = (arr: string[] | undefined) => {
          return arr && arr.length > 0 ? arr : null;
        };

        const rpcParams = {
          p_user_id: currentUserId,
          p_limit: limit,
          p_offset: offset,
          p_category_slugs: normalizeArray(categorySlugs),
          p_tool_slugs: normalizeArray(toolSlugs),
          p_tag_slugs: normalizeArray(tagSlugs),
          p_ai_model_slugs: normalizeArray(aiModelSlugs),
          p_search_query: searchQuery || null,
          p_author_id: userId || null,
          p_sort_by: sortBy,
          p_sort_order: sortOrder,
        };

        console.log("[getPrompts] About to call RPC function with params:", {
          ...rpcParams,
          p_user_id: rpcParams.p_user_id.substring(0, 8) + '...',
          p_search_query: rpcParams.p_search_query ? `"${rpcParams.p_search_query.substring(0, 30)}..."` : null
        });

        // First try with the full parameter set (updated function)
        const { data, error } = await supabase.rpc("get_prompts_with_saved_status", rpcParams, {
          signal // Pass abort signal to Supabase
        });

        console.log("[getPrompts] RPC function call completed, checking result...");

        if (error) {
          const errorMessage = error?.message || 'No error message';

          // Check if this is an AbortError - these are expected and shouldn't be logged as errors
          if (errorMessage.includes('AbortError') || errorMessage.includes('signal is aborted') || error?.code === '20') {
            console.log("[getPrompts] RPC query was aborted (expected behavior):", {
              message: errorMessage,
              code: error?.code,
              hasSignal: !!signal,
              signalAborted: signal?.aborted || false
            });
            // Return empty array for aborted requests instead of throwing
            return [];
          }

          console.error("[getPrompts] RPC function error:", {
            message: errorMessage,
            details: error.details || 'No error details',
            hint: error.hint || 'No error hint',
            code: error.code || 'No error code',
            errorType: typeof error,
            errorKeys: error ? Object.keys(error) : [],
            errorStringified: error ? JSON.stringify(error) : 'null',
            fullError: error
          });

          // Check for empty error object specifically
          if (error && typeof error === 'object' && Object.keys(error).length === 0) {
            console.error("[getPrompts] RPC returned empty error object - this is the source of the issue!");
            console.error("[getPrompts] RPC context:", {
              functionName: "get_prompts_with_saved_status",
              currentUserId: currentUserId.substring(0, 8) + '...',
              timestamp: new Date().toISOString()
            });
          }

          // Fall back to standard query
          throw new Error(`RPC function failed: ${errorMessage}`);
        }

        console.log("[getPrompts] RPC function success, data length:", Array.isArray(data) ? data.length : 0);
        const transformedData = Array.isArray(data) ? data.map(transformPromptCardWithSaved) : [];
        logPromptShortIdIssues(data as any[], "getPrompts with saved status (api-services)");
        return transformedData;
      } catch (rpcError) {
        console.warn("[getPrompts] RPC function failed, falling back to standard query:", {
          error: rpcError,
          message: rpcError instanceof Error ? rpcError.message : 'Unknown RPC error',
          errorType: typeof rpcError,
          errorKeys: rpcError && typeof rpcError === 'object' ? Object.keys(rpcError) : [],
          errorStringified: rpcError ? JSON.stringify(rpcError) : 'null',
          currentUserId: currentUserId.substring(0, 8) + '...'
        });
        // Fall back to the standard query below
      }
    }

    // Fall back to existing logic for non-authenticated users OR when RPC fails
    console.log("[getPrompts] Using standard query fallback");

    console.log("[getPrompts] About to build standard query with filters:", {
      categorySlugs: categorySlugs?.length || 0,
      toolSlugs: toolSlugs?.length || 0,
      tagSlugs: tagSlugs?.length || 0,
      aiModelSlugs: aiModelSlugs?.length || 0,
      hasSearchQuery: !!searchQuery,
      hasUserId: !!userId,
      sortBy,
      sortOrder,
      limit,
      offset
    });

    let query = supabase
      .from("prompt_card_details") // Query the optimized view
      .select("*")
      .eq("is_public", true)
      .order(sortBy, { ascending: sortOrder === "asc" })
      .range(offset, offset + limit - 1);

    console.log("[getPrompts] Base query built, applying filters...");

    if (categorySlugs && categorySlugs.length > 0) {
      console.log("[getPrompts] Applying category filter:", categorySlugs);
      query = query.in("category_slug", categorySlugs);
    }
    if (toolSlugs && toolSlugs.length > 0) {
      console.log("[getPrompts] Applying tool filter:", toolSlugs);
      query = query.in("tool_slug", toolSlugs);
    }
    if (tagSlugs && tagSlugs.length > 0) {
      console.log("[getPrompts] Applying tag filter:", tagSlugs);
      query = query.overlaps("tag_slugs_array", tagSlugs); // Assumes tag_slugs_array is TEXT[]
    }
    if (aiModelSlugs && aiModelSlugs.length > 0) {
      console.log("[getPrompts] Applying AI model filter:", aiModelSlugs);
      query = query.in("ai_model_slug", aiModelSlugs);
    }
    if (searchQuery) {
      console.log("[getPrompts] Applying search query:", searchQuery.substring(0, 50) + '...');
      query = query.textSearch("search_vector", searchQuery, { type: 'plain' });
    }
    if (userId) {
      console.log("[getPrompts] Applying author filter:", userId.substring(0, 8) + '...');
      query = query.eq("author_id", userId);
    }

    console.log("[getPrompts] All filters applied, about to execute query...");

    // Update to use signal if provided
    const queryWithSignal = signal ? query.abortSignal(signal) : query;
    console.log("[getPrompts] Executing standard query...");
    const { data, error } = await queryWithSignal;
    console.log("[getPrompts] Standard query executed, checking result...");

    if (error) {
      // Handle empty error objects more gracefully
      const errorMessage = error?.message || 'No error message';
      const errorDetails = error?.details || 'No error details';
      const errorHint = error?.hint || 'No error hint';
      const errorCode = error?.code || 'No error code';

      // Check if this is an AbortError - these are expected and shouldn't be logged as errors
      if (errorMessage.includes('AbortError') || errorMessage.includes('signal is aborted') || errorCode === '20') {
        console.log("[getPrompts] Query was aborted (expected behavior):", {
          message: errorMessage,
          code: errorCode,
          hasSignal: !!signal,
          signalAborted: signal?.aborted || false
        });
        // Return empty array for aborted requests instead of throwing
        return [];
      }

      console.error("[getPrompts] Standard query error:", {
        message: errorMessage,
        details: errorDetails,
        hint: errorHint,
        code: errorCode,
        fullError: error,
        errorType: typeof error,
        errorKeys: error ? Object.keys(error) : [],
        errorStringified: error ? JSON.stringify(error) : 'null',
        queryParams: {
          categorySlugs,
          toolSlugs,
          tagSlugs,
          aiModelSlugs,
          searchQuery,
          userId,
          sortBy,
          sortOrder,
          limit,
          offset
        },
        supabaseClientInfo: {
          url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing',
          key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'configured' : 'missing'
        }
      });

      // Check for empty error object specifically - this is likely the source of the issue
      if (!error || (typeof error === 'object' && Object.keys(error).length === 0)) {
        console.error("[getPrompts] FOUND THE ISSUE: Standard query returned empty error object!");
        console.error("[getPrompts] Standard query context:", {
          queryType: "standard",
          table: "prompt_card_details",
          hasCurrentUserId: !!currentUserId,
          timestamp: new Date().toISOString(),
          supabaseClientType: typeof supabase,
          queryBuilt: "successfully"
        });
        const meaningfulError = new Error("Database query failed with empty error object - possible connection issue");
        console.error("[getPrompts] Created meaningful error for empty error object");
        throw meaningfulError;
      }

      // Create a more descriptive error
      const descriptiveError = new Error(`Database query failed: ${errorMessage}`);
      descriptiveError.cause = error;
      throw descriptiveError;
    }
    
    console.log("[getPrompts] Standard query success, data length:", data?.length || 0);
    const transformedData = (data || []).map((collection: any) => transformPromptCard(collection));
    logPromptShortIdIssues(data as any[], "getPrompts (api-services)"); // Cast data to any[] for debug helper
    return transformedData;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : 'No stack trace';

    // Check if this is an AbortError - these are expected and shouldn't be logged as errors
    if (errorMessage.includes('AbortError') || errorMessage.includes('signal is aborted') ||
        (error instanceof Error && error.name === 'AbortError')) {
      console.log("[getPrompts] Request was aborted (expected behavior):", {
        message: errorMessage,
        hasSignal: !!signal,
        signalAborted: signal?.aborted || false
      });
      // Return empty array for aborted requests instead of throwing
      return [];
    }

    // Enhanced error serialization to handle problematic objects
    let errorStringified = 'null';
    try {
      errorStringified = error ? JSON.stringify(error, (key, value) => {
        // Handle circular references and non-serializable values
        if (typeof value === 'function') return '[Function]';
        if (typeof value === 'symbol') return '[Symbol]';
        if (value instanceof Error) return {
          name: value.name,
          message: value.message,
          stack: value.stack
        };
        return value;
      }, 2) : 'null';
    } catch (stringifyError) {
      errorStringified = `[Stringify Error: ${stringifyError instanceof Error ? stringifyError.message : 'Unknown'}]`;
    }

    console.error("Error in getPrompts (api-services):", {
      error,
      message: errorMessage,
      errorType: typeof error,
      errorConstructor: error?.constructor?.name || 'Unknown',
      errorKeys: error && typeof error === 'object' ? Object.keys(error) : [],
      errorStringified,
      stack: errorStack,
      supabaseClientInfo: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing',
        key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'configured' : 'missing'
      },
      queryParams: {
        categorySlugs,
        toolSlugs,
        tagSlugs,
        aiModelSlugs,
        searchQuery,
        userId,
        currentUserId,
        sortBy,
        sortOrder,
        limit,
        offset
      }
    });

    // If this is an empty error object, log additional debugging info
    if (error && typeof error === 'object' && Object.keys(error).length === 0) {
      console.error("[getPrompts] MAIN CATCH: Detected empty error object - this is the root cause!");
      console.error("[getPrompts] Main catch context:", {
        nodeEnv: process.env.NODE_ENV,
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'set' : 'not set',
        supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'set' : 'not set',
        userAgent: typeof window !== 'undefined' ? window.navigator?.userAgent : 'server-side',
        timestamp: new Date().toISOString(),
        errorPrototype: Object.getPrototypeOf(error)?.constructor?.name || 'Unknown'
      });
    }

    // Return empty array instead of throwing to prevent app crashes
    return [];
  }
}

export async function searchPrompts(
  searchQuery: string,
  options: {
    categorySlugs?: string[];
    toolSlugs?: string[];
    tagSlugs?: string[];
    currentUserId?: string; // NEW: for saved status
    limit?: number;
    offset?: number;
  } = {}
): Promise<PromptCard[]> {
  const { categorySlugs, toolSlugs, tagSlugs, currentUserId, limit = 10, offset = 0 } = options;
  return getPrompts({ 
    searchQuery, 
    categorySlugs, 
    toolSlugs, 
    tagSlugs, 
    currentUserId, // NEW: Pass current user ID
    limit, 
    offset, 
    sortBy: 'trending_score' 
  }); // Default sort for search
}

export async function createPrompt(
  promptData: CreatePromptData
): Promise<{ promptId: string | null; shortId: string | null; error: any }> {
  try {
    console.log('Creating prompt with data:', JSON.stringify({
      ...promptData,
      promptText: promptData.promptText?.substring(0, 50) + '...' // Truncate for logging
    }, null, 2));
    
    // Standardize AI model immediately if user entered one
    let aiModelId = promptData.aiModelId;
    
    if (promptData.userEnteredAiModel) {
      console.log('Attempting to standardize AI model:', promptData.userEnteredAiModel);
      const standardizedModelId = await standardizeAndMatchAIModel(promptData.userEnteredAiModel);
      console.log('Standardization result:', standardizedModelId);
      if (standardizedModelId) {
        // Use the standardized model ID if found
        aiModelId = standardizedModelId;
      }
    }
    
    // The issue might be with the p_user_entered_ai_model parameter
    // Let's check if the RPC function accepts this parameter
    // If not, we'll need to update the SQL function
    
    // Now that we've fixed the function overloading issue, include all parameters
    const rpcParams = {
      p_user_id: promptData.userId,
      p_category_id: promptData.categoryId,
      p_tool_id: promptData.toolId,
      p_title: promptData.title,
      p_description: promptData.description || "",
      p_prompt_text: promptData.promptText,
      p_instructions: promptData.instructions,
      p_example_input: promptData.exampleInput,
      p_example_output_text: promptData.exampleOutputText,
      p_example_output_image_url: promptData.exampleOutputImageUrl,
      p_image_url: promptData.imageUrl,
      p_is_public: promptData.isPublic,
      p_original_prompt_id: promptData.originalPromptId,
      p_tag_ids: promptData.tagIds,
      p_ai_model_id: aiModelId,
      p_user_entered_ai_model: promptData.userEnteredAiModel || null,
    };
    
    console.log('Calling RPC with params:', JSON.stringify(rpcParams, null, 2));

    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.rpc("add_prompt", rpcParams);
    
    console.log('RPC response:', { data, error });

    if (error) {
      console.error("Error creating prompt via RPC:", JSON.stringify(error, null, 2));
      return { promptId: null, shortId: null, error };
    }
    
    // The RPC now returns a single row with created_prompt_id and created_short_id
    if (data && Array.isArray(data) && data.length > 0) {
      const result = data[0];
      return { promptId: result.created_prompt_id, shortId: result.created_short_id, error: null };
    } else if (data && !Array.isArray(data)) { // If Supabase client auto-unwraps single row object
      const result = data as any; // Cast to any to access properties
      return { promptId: result.created_prompt_id, shortId: result.created_short_id, error: null };
    }

    console.warn("No data returned from add_prompt RPC or unexpected format:", data);
    return { promptId: null, shortId: null, error: "No data returned from prompt creation." };
  } catch (e) {
    console.error("Unexpected error in createPrompt service:", e);
    return { promptId: null, shortId: null, error: e };
  }
}

export async function addPromptToCollection(
  userId: string,
  promptId: string,
  collectionId: string | null // Pass null to use/create default collection
): Promise<{ success: boolean; error?: any }> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.rpc("add_prompt_to_collection", {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_collection_id: collectionId,
    });

    if (error) {
      console.error("Error adding prompt to collection:", error);
      return { success: false, error };
    }
    if (data === false) { // RPC returns boolean
      console.warn("add_prompt_to_collection RPC returned false.");
      return { success: false, error: "Failed to add prompt to collection (RPC)." };
    }
    return { success: true };
  } catch (e) {
    console.error("Unexpected error in addPromptToCollection service:", e);
    return { success: false, error: e };
  }
}

/**
 * Adds a prompt to multiple collections at once
 * Uses the add_prompt_to_multiple_collections RPC function
 */
export async function addPromptToMultipleCollections(
  userId: string,
  promptId: string,
  collectionIds: string[]
): Promise<{ success: boolean; error?: any }> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.rpc("add_prompt_to_multiple_collections", {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_collection_ids: collectionIds,
    });

    if (error) {
      console.error("Error adding prompt to multiple collections:", error);
      return { success: false, error };
    }
    if (data === false) { // RPC returns boolean
      console.warn("add_prompt_to_multiple_collections RPC returned false.");
      return { success: false, error: "Failed to add prompt to one or more collections (RPC)." };
    }
    return { success: true };
  } catch (e) {
    console.error("Unexpected error in addPromptToMultipleCollections service:", e);
    return { success: false, error: e };
  }
}

export async function getPromptById(id: string): Promise<Prompt | null> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase
      .from("prompts")
      .select(`*, category:categories(*), tool:tools(*), user:user_id(*), tags:prompt_tags(tag:tags(*)), ai_model:ai_models(*)`)
      .eq("id", id)
      .single();

    if (error || !data) {
      console.error("Error fetching prompt by ID:", error?.message);
      return null;
    }
    return transformPrompt(data);
  } catch (error) {
    console.error("Error in getPromptById (api-services):", error);
    return null;
  }
}

// Helper function to transform RPC result for getPromptByShortId
function transformPromptFromRPC(data: any): Prompt | null {
  if (!data) return null;

  // For debugging, you can log the raw data received by the transformer
  // console.log("[transformPromptFromRPC] Raw data from RPC:", JSON.stringify(data, null, 2));

  const transformed: Prompt = {
    id: data.id,
    shortId: data.short_id, // From the RPC (originally from prompts.short_id)
    short_id: data.short_id, // Keep for potential debugging
    slug: data.prompt_slug, // Use the specific prompt_slug from the RPC
    title: data.title,
    description: data.description,
    text: data.prompt_text, // CRITICAL: Map from prompt_text field
    instructions: data.instructions, // CRITICAL: Direct map
    exampleInput: data.example_input, // CRITICAL: Direct map
    exampleOutput: data.example_output_text, // CRITICAL: Map from example_output_text
    exampleOutputImageUrl: data.example_output_image_url, // Direct map
    imageUrl: data.image_url,
    isPublic: data.is_public,
    user_entered_ai_model: data.user_entered_ai_model,
    category: data.category_name && data.category_id && data.category_slug ? { 
      id: data.category_id, 
      name: data.category_name, 
      slug: data.category_slug 
    } : data.category_name || "Uncategorized",
    tool: data.tool_name && data.tool_id && data.tool_slug ? { 
      id: data.tool_id, 
      name: data.tool_name, 
      slug: data.tool_slug 
    } : undefined,
    user: data.author_username && data.user_id ? { 
      id: data.user_id, 
      username: data.author_username, 
      avatar_url: data.author_avatar_url 
    } : { 
      id: data.user_id || 'unknown-user', 
      username: 'Anonymous' 
    },
    author: data.author_username || "Anonymous", // Legacy field, ensure user object is preferred
    tags: data.tags || [], // Directly use the tags JSONB from RPC
    originalPromptId: data.original_prompt_id,
    // originalPrompt details would typically be fetched separately if needed or joined more deeply in the RPC
    rating: data.rating ?? 0,
    likeCount: data.rating ?? 0, // Map rating (net score) to likeCount for consistency
    commentCount: data.comment_count ?? 0,
    remixCount: data.remix_count ?? 0, // This should come from mv_prompt_statistics via cached_stats
    viewCount: data.view_count ?? 0, // This comes from prompts table directly
    createdAt: data.created_at,
    updatedAt: data.updated_at,
    isPremium: data.is_premium, // Assuming this field exists in your prompts table
    ai_model_id: data.ai_model_id,
    ai_model: data.ai_model_id && data.ai_model_name
      ? {
          id: data.ai_model_id,
          provider: data.ai_model_provider,
          name: data.ai_model_name, // This is ai_models.tool_name
          slug: data.ai_model_slug,
          deprecated: data.ai_model_deprecated,
          type: data.ai_model_type,
        }
      : null,
  };
  
  // console.log("[transformPromptFromRPC] Transformed prompt:", JSON.stringify(transformed, null, 2));
  return transformed;
}

export async function getPromptByShortId(shortId: string): Promise<Prompt | null> {
  try {
    console.log(`[api-services/getPromptByShortId] Using cached function for short_id: "${shortId}"`);

    // Use the new cached RPC function instead of multiple queries
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase
      .rpc('get_prompt_with_stats_cached', { p_short_id: shortId })
      .single();

    if (error) {
      console.error(`[api-services/getPromptByShortId] RPC error:`, error);
      return null;
    }

    if (!data) {
      console.warn(`[api-services/getPromptByShortId] No data returned for short_id "${shortId}"`);
      return null;
    }

    // Transform the data
    return transformPromptFromRPC(data);
  } catch (e) {
    console.error(`[api-services/getPromptByShortId] Unexpected error:`, e);
    return null;
  }
}

// --- Categories, Tools, Tags ---
export async function getCategories(): Promise<Category[]> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.from("categories").select("*").order("name");
    if (error) throw error;
    return (data || []).map(transformCategory);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
}

export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.from("categories").select("*").eq("slug", slug).single();
    if (error || !data) return null;
    return transformCategory(data);
  } catch (error) {
    console.error("Error fetching category by slug:", error);
    return null;
  }
}

export async function getTools(): Promise<Tool[]> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.from("tools").select("*").order("name");
    if (error) throw error;
    return (data || []).map(transformTool);
  } catch (error) {
    console.error("Error fetching tools:", error);
    return [];
  }
}

export async function getToolBySlug(slug: string): Promise<Tool | null> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.from("tools").select("*").eq("slug", slug).single();
    if (error || !data) return null;
    return transformTool(data);
  } catch (error) {
    console.error("Error fetching tool by slug:", error);
    return null;
  }
}

export async function getTags(): Promise<Tag[]> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.from("tags").select("*").order("name");
    if (error) throw error;
    return (data || []).map(transformTag);
  } catch (error) {
    console.error("Error fetching tags:", error);
    return [];
  }
}

export async function getTagBySlug(slug: string): Promise<Tag | null> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.from("tags").select("*").eq("slug", slug).single();
    if (error || !data) return null;
    return transformTag(data);
  } catch (error) {
    console.error("Error fetching tag by slug:", error);
    return null;
  }
}

// --- Profiles ---
export async function getProfileById(userId: string): Promise<Profile | null> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.from("profiles").select("*").eq("id", userId).single();
    if (error || !data) return null;
    return transformProfile(data);
  } catch (error) {
    console.error("Error fetching profile by ID:", error);
    return null;
  }
}

export async function getProfileByUsername(username: string): Promise<Profile | null> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.from("profiles").select("*").eq("username", username).single();
    if (error || !data) return null;
    return transformProfile(data);
  } catch (error) {
    console.error("Error fetching profile by username:", error);
    return null;
  }
}

// --- Collections ---
/**
 * Get user collections with full transformation and flexible options
 * This is the general-purpose collection fetching function
 */
export async function getUserCollections(
  userId: string,
  options: {
    includePrivate?: boolean;
    limit?: number;
    offset?: number;
    sortBy?: "created_at" | "prompt_count" | "name";
    sortOrder?: "asc" | "desc";
    purpose?: "dialog" | "general";
  } = {}
): Promise<Collection[]> {
  // For dialog purpose, use the optimized function
  if (options.purpose === "dialog") {
    const collections = await getUserCollectionsForDialog(userId);
    return collections;
  }
  
  console.log(`[getUserCollections] START - userId: ${userId}, purpose: ${options.purpose || 'general'}`);
  
  const {
    includePrivate = false,
    limit = 10,
    offset = 0,
    sortBy = "created_at",
    sortOrder = "desc",
  } = options;

  try {
    // Create query for general purpose
    let query = supabase
      .from("collections")
      .select(`
        id,
        user_id,
        name,
        description,
        icon,
        is_public,
        is_default,
        default_type,
        prompt_count,
        view_count,
        created_at,
        updated_at,
        user:profiles!collections_user_id_fkey (username, avatar_url)
      `)
      .eq("user_id", userId)
      .order(sortBy, { ascending: sortOrder === "asc" })
      .range(offset, offset + limit - 1);

    // Apply public/private filter for general purpose
    if (!includePrivate) {
      query = query.eq("is_public", true);
    }

    const { data, error } = await query;

    if (error) {
      console.error(`[getUserCollections] Error fetching collections:`, error.message);
      throw error;
    }

    // Transform collections for the general case
    const transformedCollections = (data || []).map((collection: any) => transformCollection(collection));
    return transformedCollections;
  } catch (error) {
    console.error(`[getUserCollections] Error in getUserCollections:`, error);
    return [];
  }
}

/**
 * Get user collections for dialog use
 * Simple, direct fetch with minimal processing and no transformations
 */
export async function getUserCollectionsForDialog(userId: string, signal?: AbortSignal): Promise<Collection[]> {
  const startTime = performance.now();
  
  try {
    // SIMPLIFIED QUERY: Single optimized query that directly returns what we need
    // 1. Only get essential fields needed for the dialog
    // 2. Exclude "My Prompts" collection
    // 3. No transformations - return data in the format needed by the UI
    let query = supabase
      .from("collections")
      .select(`
        id,
        name,
        description,
        icon,
        is_public,
        is_default,
        default_type,
        prompt_count
      `)
      .eq("user_id", userId)
      .or('default_type.is.null,default_type.neq.my_prompts')
      .order("is_default", { ascending: false }) // Default collections first
      .order("name", { ascending: true });      // Then alphabetical

    if (signal) {
      query = query.abortSignal(signal);
    }
    const { data, error } = await query;
    
    const queryTime = performance.now() - startTime;
    
    if (process.env.NODE_ENV !== "production" && queryTime > 1000) {
      console.warn(`[getUserCollectionsForDialog] Slow query execution time: ${queryTime.toFixed(2)}ms`);
    }

    if (error) {
      // Don't log AbortError as it's expected when the user closes the dialog quickly
      if (error?.name === 'AbortError' || signal?.aborted) {
        throw error; // Re-throw abort errors without logging
      }
      console.error(`[getUserCollectionsForDialog] Error fetching collections:`, error.message);
      throw error;
    }

    // SIMPLIFIED PROCESSING: Map directly to Collection interface without full transformation
    const collections = (data || []).map((c: any) => ({
      id: String(c.id), // Convert to string to match Collection interface
      userId: userId,
      name: c.name || '',
      description: c.description,
      icon: c.icon,
      isPublic: c.is_public || false,
      isDefault: c.is_default || false,
      defaultType: c.default_type,
      promptCount: c.prompt_count || 0,
      viewCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    })) as Collection[];
    
    return collections;
  } catch (error: any) {
    // Don't log AbortError as it's expected when the user closes the dialog quickly
    if (error?.name === 'AbortError' || signal?.aborted) {
      throw error; // Re-throw abort errors without logging
    }
    
    console.error(`[getUserCollectionsForDialog] Error fetching collections:`, error?.message || error);
    return []; // Return empty array on error to prevent UI crashes
  }
}

export async function createCollection(
  userId: string,
  collectionData: {
    name: string;
    description?: string | null;
    imageFile?: File | null;
    is_public: boolean;
  }
): Promise<Collection> {
  try {
    console.log("[createCollection] Starting with data:", JSON.stringify(collectionData));
    console.log("[createCollection] User ID:", userId);
    
    let iconUrl: string | null = null;

    // Handle image upload logic
    if (collectionData.imageFile) {
      const file = collectionData.imageFile;
      const fileName = `${userId}/${Date.now()}-${file.name.replace(/[^a-zA-Z0-9._-]+/g, '_')}`;
      console.log("[createCollection] Uploading image:", fileName);
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('collection-images')
        .upload(fileName, file, { cacheControl: '3600', upsert: false });

      if (uploadError) {
        console.error("[createCollection] Image upload error:", uploadError);
        throw uploadError;
      }
      iconUrl = supabase.storage.from('collection-images').getPublicUrl(fileName).data.publicUrl;
      console.log("[createCollection] Image uploaded successfully, URL:", iconUrl);
    }

    console.log("[createCollection] Calling create_custom_collection RPC with params:", {
      p_user_id: userId,
      p_name: collectionData.name,
      p_description: collectionData.description,
      p_icon_url: iconUrl,
      p_is_public: collectionData.is_public,
    });

    // Call RPC without color parameter
    const { data, error: rpcError } = await supabase
      .rpc("create_custom_collection", {
        p_user_id: userId,
        p_name: collectionData.name,
        p_description: collectionData.description,
        p_icon_url: iconUrl,
        p_is_public: collectionData.is_public,
      })
      .select("*")
      .single();

    // Type assertion to help TypeScript understand the structure
    const newCollectionData = data as any;
    
    console.log("[createCollection] RPC response data:", newCollectionData);
    
    if (rpcError) {
      if (rpcError.code === '23505') { // Unique violation
        console.error("[createCollection] Unique violation error:", rpcError);
        throw new Error(`A collection with the name "${collectionData.name}" might already exist. Try a different name.`);
      }
      console.error("[createCollection] Error creating collection via RPC:", rpcError.message);
      throw rpcError;
    }
    
    if (!newCollectionData) {
      console.error("[createCollection] Collection creation returned no data");
      throw new Error("Collection creation returned no data.");
    }
    
    // Log the collection data structure to help debug
    console.log("[createCollection] Collection data structure:", {
      hasId: Boolean(newCollectionData?.id),
      idValue: newCollectionData?.id,
      idType: typeof newCollectionData?.id,
      keys: newCollectionData ? Object.keys(newCollectionData) : [],
      isObject: typeof newCollectionData === 'object',
      isNull: newCollectionData === null
    });
    
    // Check if we need to fetch the collection (only if truly missing data)
    if (!newCollectionData || typeof newCollectionData !== 'object' || !newCollectionData.id) {
      console.log("[createCollection] Need to fetch collection - data is incomplete");
      
      // Fetch the collection we just created to ensure we have complete data
      const { data: fetchedCollection, error: fetchError } = await supabase
        .from('collections')
        .select('*')
        .eq('user_id', userId)
        .eq('name', collectionData.name)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();
        
      if (fetchError || !fetchedCollection) {
        console.error("[createCollection] Failed to fetch the created collection:", fetchError);
        throw new Error("Failed to retrieve the created collection.");
      }
      
      console.log("[createCollection] Successfully fetched the created collection:", fetchedCollection);
      // Use the fetched collection instead of trying to modify the constant
      return transformCollection(fetchedCollection);
    }
    
    console.log("[createCollection] Successfully created collection, passing to transformer:", newCollectionData);
    const transformedCollection = transformCollection(newCollectionData);
    console.log("[createCollection] Transformed collection:", transformedCollection);
    return transformedCollection;
  } catch (error) {
    console.error("[createCollection] Error in createCollection service:", error);
    throw error;
  }
}

/**
 * Update an existing collection
 * Note: Default collections cannot be edited
 */
export async function updateCollection(
  userId: string,
  collectionId: string,
  collectionData: {
    name?: string;
    description?: string | null;
    imageFile?: File | null;
    removeCurrentImage?: boolean;
    icon?: string | null;
    is_public?: boolean;
  }
): Promise<Collection> {
  console.log('[api-services] updateCollection called with:', {
    userId,
    collectionId,
    collectionData: {
      name: collectionData.name,
      description: collectionData.description,
      imageFile: collectionData.imageFile ? {
        name: collectionData.imageFile.name,
        type: collectionData.imageFile.type,
        size: collectionData.imageFile.size
      } : null,
      removeCurrentImage: collectionData.removeCurrentImage,
      icon: collectionData.icon,
      is_public: collectionData.is_public
    }
  });
  
  try {
    // Handle image update logic
    let iconUrlUpdate = collectionData.icon;
    console.log('[api-services] Initial iconUrlUpdate:', iconUrlUpdate);
    
    if (collectionData.imageFile) {
    console.log('[api-services] Processing image file upload');
    const file = collectionData.imageFile;
    
    // Validate file
    if (!file.type.startsWith('image/')) {
      console.error('[api-services] Invalid file type:', file.type);
      throw new Error('Only image files are allowed');
    }
    
    // Create a unique filename to avoid collisions
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 10);
    const safeFileName = file.name.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_.-]/g, '');
    const fileName = `${userId}/${timestamp}-${randomId}-${safeFileName}`;
    
    console.log('[api-services] Generated storage path:', fileName);
    
    // Convert File to Blob for Supabase storage upload
    let fileBlob;
    try {
      fileBlob = file;
      console.log('[api-services] File prepared for upload:', {
        size: fileBlob.size,
        type: fileBlob.type
      });
    } catch (error) {
      console.error('[api-services] Error preparing file for upload:', error);
      throw new Error('Failed to process image file');
    }
    
    console.log('[api-services] Starting Supabase storage upload');
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('collection-images')
      .upload(fileName, fileBlob, { 
        cacheControl: '3600', 
        upsert: true, // Use upsert:true to overwrite if file exists
        contentType: file.type // Explicitly set content type
      });

    if (uploadError) {
      console.error('[api-services] Supabase storage upload error:', uploadError);
      throw uploadError;
    }
    
    console.log('[api-services] Upload successful, data:', uploadData);
    const publicUrlResult = supabase.storage.from('collection-images').getPublicUrl(fileName);
    console.log('[api-services] Public URL result:', publicUrlResult);
    
    iconUrlUpdate = publicUrlResult.data.publicUrl;
    console.log('[api-services] Set iconUrlUpdate to:', iconUrlUpdate);
    } else if (collectionData.removeCurrentImage) {
      console.log('[api-services] Removing current image (setting iconUrlUpdate to null)');
      iconUrlUpdate = null;
    }

    // Prepare RPC parameters - no color parameter
    const rpcParams: any = {
      p_collection_id: collectionId,
      p_user_id: userId,
    };
    
    if (collectionData.name !== undefined) rpcParams.p_name = collectionData.name;
    if (collectionData.description !== undefined) rpcParams.p_description = collectionData.description;
    if (iconUrlUpdate !== undefined) {
      rpcParams.p_icon_url = iconUrlUpdate;
      console.log('[api-services] Added p_icon_url to RPC params:', iconUrlUpdate);
    }
    if (collectionData.is_public !== undefined) rpcParams.p_is_public = collectionData.is_public;

    console.log('[api-services] Final RPC parameters:', rpcParams);

    // Call RPC
    console.log('[api-services] Calling update_user_collection RPC');
    
    // When calling an RPC function that returns a table type (SETOF collections),
    // we need to handle it differently than a scalar return type
    const { data: updatedCollectionData, error: rpcError } = await supabase
      .rpc("update_user_collection", rpcParams);
      
    console.log('[api-services] RPC response:', { data: updatedCollectionData, error: rpcError });

    if (rpcError) {
      console.error('[api-services] RPC error details:', {
        code: rpcError.code,
        message: rpcError.message,
        details: rpcError.details,
        hint: rpcError.hint
      });
      
      if (rpcError.code === '23505') { // Unique violation
        throw new Error(`A collection with the name "${collectionData.name}" might already exist. Try a different name.`);
      } else if (rpcError.message && rpcError.message.includes('default collection')) {
        throw new Error("Default collections cannot be edited.");
      }
      console.error("[api-services] Error updating collection via RPC:", rpcError.message);
      throw rpcError;
    }
    
    // For RPC functions that return SETOF, the data will be an array
    if (!updatedCollectionData || !Array.isArray(updatedCollectionData) || updatedCollectionData.length === 0) {
      console.error('[api-services] RPC returned no data:', updatedCollectionData);
      
      // Since we've already uploaded the image, let's fetch the collection directly
      console.log('[api-services] Falling back to direct collection fetch');
      const { data: fetchedCollection, error: fetchError } = await supabase
        .from("collections")
        .select("*")
        .eq("id", collectionId)
        .single();
        
      if (fetchError || !fetchedCollection) {
        console.error('[api-services] Failed to fetch collection after update:', fetchError);
        throw new Error("Collection update could not be verified.");
      }
      
      console.log('[api-services] Successfully fetched collection after update:', fetchedCollection);
      return transformCollection(fetchedCollection);
    }
    
    // Take the first item from the array (should be the only one)
    const updatedCollection = updatedCollectionData[0];
    console.log('[api-services] Successfully updated collection, raw data:', updatedCollection);
    const transformedCollection = transformCollection(updatedCollection);
    console.log('[api-services] Transformed collection:', transformedCollection);
    
    return transformedCollection;
  } catch (error) {
    console.error("[api-services] Error in updateCollection service:", error);
    throw error;
  }
}

/**
 * Remove a prompt from a collection
 * This function allows users to remove prompts from their collections
 */
export async function removePromptFromCollection(
  userId: string,
  promptId: string,
  collectionId: string
): Promise<{ success: boolean; error?: any }> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.rpc("remove_prompt_from_collection", {
      p_user_id: userId,
      p_prompt_id: promptId,
      p_collection_id: collectionId,
    });

    if (error) {
      console.error("Error removing prompt from collection:", error);
      return { success: false, error };
    }
    
    if (data === false) { // RPC returns boolean
      console.warn("remove_prompt_from_collection RPC returned false.");
      return { success: false, error: "Failed to remove prompt from collection (RPC)." };
    }
    
    return { success: true };
  } catch (e) {
    console.error("Unexpected error in removePromptFromCollection service:", e);
    return { success: false, error: e };
  }
}

/**
 * Gets the collection IDs that a prompt belongs to for a specific user
 * This is used to determine if a prompt is saved in any of the user's collections
 */
export async function getPromptCollectionMembership(
  userId: string,
  promptId: string,
  signal?: AbortSignal
): Promise<{ collectionIds: string[]; error?: any }> {
  try {
    // More efficient approach: directly query the collection_prompts table with a join
    // This eliminates the need for two separate queries
    let query = supabase
      .from("collection_prompts")
      .select(`
        collection_id,
        collections!inner(user_id)
      `)
      .eq("prompt_id", promptId)
      .eq("collections.user_id", userId);

    if (signal) {
      query = query.abortSignal(signal);
    }
    const { data: memberships, error: membershipsError } = await query;

    if (membershipsError) {
      // Don't log AbortError as it's expected when the user closes the dialog quickly
      if (membershipsError?.name === 'AbortError' || signal?.aborted) {
        return { collectionIds: [], error: membershipsError }; // Return gracefully for abort errors
      }
      console.error(`[getPromptCollectionMembership] Error:`, membershipsError.message);
      return { collectionIds: [], error: membershipsError };
    }

    // Extract collection IDs from the results
    const collectionIds = memberships ? memberships.map((m: any) => m.collection_id as string) : [];
    
    return { collectionIds, error: null };
  } catch (error: any) {
    // Don't log AbortError as it's expected when the user closes the dialog quickly
    if (error?.name === 'AbortError' || signal?.aborted) {
      return { collectionIds: [], error }; // Return gracefully for abort errors
    }
    
    console.error(`[getPromptCollectionMembership] Error:`, error?.message || error);
    return { collectionIds: [], error };
  }
}

export async function updateNotificationPreferences(
  userId: string,
  preferences: {
    email_on_comment?: boolean;
    email_on_reply?: boolean;
    email_on_like?: boolean;
    email_product_updates?: boolean;
  }
): Promise<Profile> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase
      .from("profiles")
      .update({
        ...preferences,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId)
      .select()
      .single();

    if (error) {
      console.error("Error updating notification preferences:", error.message);
      throw error;
    }
    if (!data) throw new Error("Failed to update preferences, no data returned.");
    return data as unknown as Profile;
  } catch (error) {
    console.error("Error in updateNotificationPreferences:", error);
    throw error;
  }
}

export async function getNotificationPreferences(userId: string): Promise<Partial<Profile> | null> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase
      .from("profiles")
      .select("email_on_comment, email_on_reply, email_on_like, email_product_updates")
      .eq("id", userId)
      .single();

    if (error) {
      console.error("Error fetching notification preferences:", error.message);
      throw error;
    }
    return data as unknown as Partial<Profile> | null;
  } catch (error) {
    console.error("Error in getNotificationPreferences:", error);
    return null;
  }
}

// --- Notifications ---
export async function getNotifications(
  userId: string,
  options: {
    limit?: number;
    offset?: number;
    unreadOnly?: boolean;
  } = {}
): Promise<Notification[]> {
  const { limit = 10, offset = 0, unreadOnly = false } = options;

  try {
    let query = supabase
      .from("notifications")
      .select(`
        id,
        recipient_user_id,
        actor_user_id,
        type,
        entity_id,
        entity_type,
        entity_title,
        link,
        is_read,
        created_at,
        actor:profiles!notifications_actor_user_id_fkey (username, avatar_url),
        prompt:entity_id ( short_id, title, category:category_id(slug), tool:tool_id(slug), primary_tag:primary_tag_id(slug) )
      `)
      // The join for prompt might need adjustment based on how entity_id links to prompts table
      // Assuming entity_id is prompt.id and entity_type is 'prompt'
      .eq("recipient_user_id", userId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (unreadOnly) {
      query = query.eq("is_read", false);
    }

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching notifications:", error.message);
      throw error;
    }
    const notificationsData: any[] = data || []; // Explicitly type data
    return notificationsData.map(n => ({
      id: n.id,
      recipient_user_id: n.recipient_user_id,
      actor_user_id: n.actor_user_id,
      actor: n.actor ? { username: n.actor.username, avatar_url: n.actor.avatar_url } : null,
      type: n.type,
      entity_id: n.entity_id,
      entity_type: n.entity_type,
      entity_title: n.entity_title || n.prompt?.title,
      link: n.link, // Prefer pre-generated link
      prompt_short_id: n.prompt?.short_id, // For fallback URL generation
      // ... other prompt slug fields from n.prompt if needed for generatePromptUrl
      is_read: n.is_read,
      created_at: n.created_at,
    })) as Notification[];
  } catch (error) {
    console.error("Error in getNotifications:", error);
    return [];
  }
}

export async function markNotificationAsRead(notificationId: string, userId: string): Promise<boolean> {
    const supabase = getSupabaseClientSafe();
    const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .eq('recipient_user_id', userId); // Ensure user can only mark their own
    if (error) {
        console.error("Error marking notification as read:", error);
        return false;
    }
    return true;
}

export async function markAllNotificationsAsRead(userId: string): Promise<boolean> {
    const supabase = getSupabaseClientSafe();
    const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('recipient_user_id', userId)
        .eq('is_read', false);
    if (error) {
        console.error("Error marking all notifications as read:", error);
        return false;
    }
    return true;
}

// --- For Homepage ---
export async function getCategoriesWithPromptCount(): Promise<Category[]> {
  try {
    const supabase = getSupabaseClientSafe();
    // Get all categories without prompt count
    const { data, error } = await supabase
      .from("categories")
      .select("*")
      .order("name");

    if (error) {
      console.error("Error fetching categories:", error);
      throw error;
    }

    // Just transform the categories without adding prompt count
    return (data || []).map(transformCategory);
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
}

export async function getToolsWithPromptCount(): Promise<Tool[]> {
  try {
    const supabase = getSupabaseClientSafe();
    // Get all tools without prompt count
    const { data, error } = await supabase
      .from("tools")
      .select("*")
      .order("name");

    if (error) {
      console.error("Error fetching tools:", error);
      throw error;
    }

    // Just transform the tools without adding prompt count
    return (data || []).map(transformTool);
  } catch (error) {
    console.error("Error fetching tools:", error);
    return [];
  }
}

export async function getTagsWithPromptCount(): Promise<Tag[]> {
  try {
    const supabase = getSupabaseClientSafe();
    // Get all tags without prompt count
    const { data, error } = await supabase
      .from("tags")
      .select("*")
      .order("name");

    if (error) {
      console.error("Error fetching tags:", error);
      throw error;
    }

    // Just transform the tags without adding prompt count
    return (data || []).map(transformTag);
  } catch (error) {
    console.error("Error fetching tags:", error);
    return [];
  }
}

// Define a type for the data fetched from the comment_display_details view
interface CommentDisplayDetails {
  id: string;
  prompt_id: string;
  parent_comment_id: string | null;
  user_id: string;
  author_username: string;
  author_avatar_url: string | null;
  text: string;
  like_count: number;
  dislike_count: number;
  created_at: string;
  updated_at: string;
  // Add other fields from the view if necessary
}

// --- Comments ---
export async function getCommentsForPrompt(promptShortIdOrId: string): Promise<Comment[]> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data: topLevelCommentsData, error: topLevelError } = await supabase
      .from("comment_display_details") // Use the view
      .select("*")
      .eq("prompt_id", promptShortIdOrId) // Assuming prompt_id in view can be short_id or UUID
      .is("parent_comment_id", null)
      .order("like_count", { ascending: false }); // Default sort by top

    if (topLevelError) {
      console.error("Error fetching top-level comments:", topLevelError.message);
      throw topLevelError;
    }

    const topLevelComments: CommentDisplayDetails[] = (topLevelCommentsData || []) as unknown as CommentDisplayDetails[];

    // Fetch replies separately
    const { data: repliesData, error: repliesError } = await supabase
      .from("comment_display_details")
      .select("*")
      .eq("prompt_id", promptShortIdOrId)
      .not("parent_comment_id", "is", null) // Only fetch replies
      .order("created_at", { ascending: true }); // Order replies chronologically

    if (repliesError) {
      console.error("Error fetching replies:", repliesError.message);
      throw repliesError;
    }

    const replies: CommentDisplayDetails[] = (repliesData || []) as unknown as CommentDisplayDetails[];

    // Group replies by parent_comment_id
    const repliesByParentId = replies.reduce(
      (acc, reply) => {
        const parentId = reply.parent_comment_id;
        if (parentId) { // Ensure parentId is not null
          if (!acc[parentId]) {
            acc[parentId] = [];
          }
          acc[parentId].push(reply);
        }
        return acc;
      },
      {} as Record<string, CommentDisplayDetails[]>
    );

    // Map fetched data to the Comment type and attach replies
    const comments: Comment[] = topLevelComments.map((commentData) => ({
      id: commentData.id,
      user_id: commentData.user_id,
      prompt_id: commentData.prompt_id,
      parent_comment_id: commentData.parent_comment_id,
      text: commentData.text,
      like_count: commentData.like_count || 0,
      dislike_count: commentData.dislike_count || 0,
      created_at: commentData.created_at,
      updated_at: commentData.updated_at,
      // Map user details from the view
      user: {
        id: commentData.user_id, // Assuming user_id is the user's ID
        username: commentData.author_username || "Anonymous",
        avatar_url: commentData.author_avatar_url,
      },
      // Map other properties needed for the Comment type
      likes: commentData.like_count || 0, // Assuming 'likes' is the same as 'like_count'
      dislikes: commentData.dislike_count || 0, // Assuming 'dislikes' is the same as 'dislike_count'
      recommended: (commentData.like_count || 0) > (commentData.dislike_count || 0), // Example logic
      liked_by_user: false, // This needs to be determined based on user's likes, potentially in the component
      replies: (repliesByParentId[commentData.id] || []).map(replyData => ({
         id: replyData.id,
         user_id: replyData.user_id,
         prompt_id: replyData.prompt_id,
         parent_comment_id: replyData.parent_comment_id,
         text: replyData.text,
         like_count: replyData.like_count || 0,
         dislike_count: replyData.dislike_count || 0,
         created_at: replyData.created_at,
         updated_at: replyData.updated_at,
         user: {
           id: replyData.user_id,
           username: replyData.author_username || "Anonymous",
           avatar_url: replyData.author_avatar_url,
         },
         likes: replyData.like_count || 0,
         dislikes: replyData.dislike_count || 0,
         recommended: (replyData.like_count || 0) > (replyData.dislike_count || 0),
         liked_by_user: false, // Needs to be determined based on user's likes
         replies: [], // Replies of replies are not currently supported in this structure
      })),
    }));

    return comments;
  } catch (error) {
    console.error("Error in getCommentsForPrompt:", error);
    return [];
  }
}

// --- AI Models ---
export async function getAllAIModels(): Promise<AIModel[]> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase
      .from("ai_models")
      .select("*")
      .order("provider");

    if (error) {
      console.error("Error fetching AI models:", error.message);
      throw error;
    }

    return data?.map((model: any) => ({
      id: Number(model.id),
      provider: String(model.provider),
      name: String(model.tool_name),
      slug: String(model.slug),
      type: model.type ? String(model.type) : null,
      deprecated: Boolean(model.deprecated),
      tool_id: Number(model.tool_id),
    })) || [];
  } catch (error) {
    console.error("Error in getAllAIModels:", error);
    return [];
  }
}

export async function getAIModelsForTool(toolSlug: string): Promise<AIModel[]> {
  try {
    const supabase = getSupabaseClientSafe();
    // First get the tool ID from the slug
    const { data: toolData, error: toolError } = await supabase
      .from("tools")
      .select("id, name")
      .eq("slug", toolSlug)
      .single();

    if (toolError) {
      console.error(`Error fetching tool ID for slug ${toolSlug}:`, toolError.message);
      throw toolError;
    }

    if (!toolData) {
      console.error(`No tool found with slug ${toolSlug}`);
      return [];
    }

    // Now fetch AI models for this tool ID
    const { data, error } = await supabase
      .from("ai_models")
      .select("*")
      .eq("tool_id", toolData.id as number); // Cast to number since we know it's from the database

    if (error) {
      console.error(`Error fetching AI models for tool ${toolSlug}:`, error.message);
      throw error;
    }

    // Ensure data is not null/undefined and type assertion to help TypeScript understand the structure
    const safeData = data || [];
    const typedData = safeData as Array<{
      id: number;
      provider: string;
      tool_name: string;
      slug?: string;
      type?: string;
      deprecated: boolean;
      tool_id: number;
      tool?: string;
    }>;

    return typedData.map((model) => ({
      id: Number(model.id),
      provider: String(model.provider || ''),
      name: String(model.tool_name || ''),  // This is the actual model name in the database
      slug: String(model.slug || model.id || ''),
      type: model.type ? String(model.type) : null,
      tool_id: Number(model.tool_id),
      tool_slug: toolSlug,
      tool_name: String(model.tool || toolData.name || ''),
      deprecated: Boolean(model.deprecated),
    }));
  } catch (error) {
    console.error(`Error in getAIModelsForTool for ${toolSlug}:`, error);
    return [];
  }
}

export async function getAIModelBySlug(slug: string): Promise<AIModel | null> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase
      .from("ai_models")
      .select("*")
      .eq("slug", slug)
      .single();

    if (error || !data) {
      console.error(`Error fetching AI model by slug ${slug}:`, error?.message);
      return null;
    }

    return {
      id: Number(data.id),
      provider: String(data.provider),
      name: String(data.tool_name),
      slug: String(data.slug),
      type: data.type ? String(data.type) : null,
      deprecated: Boolean(data.deprecated),
      tool_id: Number(data.tool_id),
    };
  } catch (error) {
    console.error(`Error in getAIModelBySlug for ${slug}:`, error);
    return null;
  }
}

// --- Related Prompts ---
export async function updatePrompt(
  promptId: string,
  userId: string, // For authorization
  promptData: Partial<CreatePromptData>
): Promise<{ success: boolean; error: any; updatedSlug?: string }> {
  try {
    // Fetch the existing prompt to verify ownership and get short_id
    const { data: existingPrompt, error: fetchError } = await supabase
      .from('prompts')
      .select('user_id, short_id, slug, title, category_id, tool_id, primary_tag_id')
      .eq('id', promptId)
      .single();

    if (fetchError || !existingPrompt) {
      console.error('Error fetching existing prompt or prompt not found:', fetchError?.message);
      return { success: false, error: fetchError || new Error("Prompt not found") };
    }

    if (existingPrompt.user_id !== userId) {
      console.error('User not authorized to update this prompt.');
      return { success: false, error: new Error("Unauthorized") };
    }

    // Prepare data for update, including slug regeneration if title changed
    const updatePayload: any = {
      updated_at: new Date().toISOString(),
      updated_by_user_id: userId,
    };

    if (promptData.title !== undefined) updatePayload.title = promptData.title;
    if (promptData.description !== undefined) updatePayload.description = promptData.description;
    if (promptData.promptText !== undefined) updatePayload.prompt_text = promptData.promptText;
    if (promptData.instructions !== undefined) updatePayload.instructions = promptData.instructions;
    if (promptData.exampleInput !== undefined) updatePayload.example_input = promptData.exampleInput;
    if (promptData.exampleOutputText !== undefined) updatePayload.example_output_text = promptData.exampleOutputText;
    if (promptData.imageUrl !== undefined) updatePayload.image_url = promptData.imageUrl; // Already handles null
    if (promptData.isPublic !== undefined) updatePayload.is_public = promptData.isPublic;
    if (promptData.categoryId !== undefined) updatePayload.category_id = promptData.categoryId;
    if (promptData.toolId !== undefined) updatePayload.tool_id = promptData.toolId;
    if (promptData.aiModelId !== undefined) updatePayload.ai_model_id = promptData.aiModelId;
    if (promptData.userEnteredAiModel !== undefined) updatePayload.user_entered_ai_model = promptData.userEnteredAiModel;

    let newSlug: string = String(existingPrompt?.slug || ''); // Keep existing slug by default, convert to string

    // Regenerate slug if title, category, or tool changes
    // Or if a primary tag is involved and it changes (more complex, handle if needed)
    const currentTitle = promptData.title || (existingPrompt?.title as string);
    const currentCategoryId = promptData.categoryId || (existingPrompt?.category_id as number);
    const currentToolId = promptData.toolId || (existingPrompt?.tool_id as number);
    // For slug generation, we need category and tool slugs, and first tag slug
    const { data: categoryData } = await supabase.from('categories').select('slug').eq('id', currentCategoryId).single();
    const { data: toolData } = await supabase.from('tools').select('slug').eq('id', currentToolId).single();
    
    // Type assertions for categoryData and toolData
    const categorySlug = categoryData?.slug as string | undefined;
    const toolSlug = toolData?.slug as string | undefined;
    
    let firstTagSlug = 'untagged';
    if (promptData.tagIds && promptData.tagIds.length > 0) {
        const { data: tagData } = await supabase.from('tags').select('slug').eq('id', promptData.tagIds[0]).single();
        if (tagData) firstTagSlug = tagData.slug as string;
    } else if (existingPrompt?.primary_tag_id) {
        const { data: tagData } = await supabase.from('tags').select('slug').eq('id', existingPrompt.primary_tag_id).single();
        if (tagData) firstTagSlug = tagData.slug as string;
    }

    if (categorySlug && toolSlug) {
        newSlug = `prompt/${categorySlug}/${toolSlug}/${firstTagSlug}/${createTitleSlug(currentTitle)}/${existingPrompt?.short_id as string}`;
        updatePayload.slug = newSlug;
    }

    // Update the prompt table
    const { error } = await supabase
      .from('prompts')
      .update(updatePayload)
      .eq('id', promptId);
    
    if (error) {
      console.error('Error updating prompt:', error.message);
      return { success: false, error };
    }
    
    // Handle tags: delete existing and insert new ones
    if (promptData.tagIds !== undefined) {
      // First delete all existing tag associations
      const { error: deleteError } = await supabase
        .from('prompt_tags')
        .delete()
        .eq('prompt_id', promptId);
      
      if (deleteError) {
        console.error('Error deleting existing tags:', deleteError.message);
        return { success: false, error: deleteError };
      }
      
      // Then insert new tag associations if there are any tags
      if (promptData.tagIds.length > 0) {
        const newPromptTags = promptData.tagIds.map(tagId => ({
          prompt_id: promptId,
          tag_id: tagId,
        }));
        const { error: insertError } = await supabase
          .from('prompt_tags')
          .insert(newPromptTags);

        if (insertError) {
          console.error('Error inserting new tags:', insertError.message);
          return { success: false, error: insertError };
        }
      }
      
      // Update primary_tag_id on prompts table if tags were changed
      if (promptData.tagIds.length > 0) {
        await supabase.from('prompts').update({ primary_tag_id: promptData.tagIds[0] }).eq('id', promptId);
      } else {
        await supabase.from('prompts').update({ primary_tag_id: null }).eq('id', promptId);
      }
    }
    
    return { success: true, error: null, updatedSlug: newSlug };
  } catch (error: unknown) {
    console.error('Error in updatePrompt:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, error: errorMessage };
  }
}

// --- Related Prompts ---
export async function getRelatedPromptsForDisplay(sourcePromptShortId: string, limit = 6): Promise<PromptCard[]> {
  try {
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.rpc("get_related_prompts", {
      p_source_prompt_short_id: sourcePromptShortId,
      p_limit: limit,
    });

    if (error) {
      console.error("Error fetching related prompts via RPC:", error);
      throw error;
    }
    
    return (data as any[] || []).map(transformPromptCard); 
  } catch (e) {
    console.error("Error in getRelatedPromptsForDisplay service:", e);
    return [];
  }
}

// --- Delete Prompt ---
export async function deletePrompt(
  promptId: string,
  userId: string // For authorization check
): Promise<{ success: boolean; error?: any }> {
  try {
    // First, verify ownership
    const { data: existingPrompt, error: fetchError } = await supabase
      .from('prompts')
      .select('user_id, title')
      .eq('id', promptId)
      .single();

    if (fetchError || !existingPrompt) {
      console.error('Error fetching prompt or prompt not found:', fetchError?.message);
      return { success: false, error: fetchError || new Error("Prompt not found") };
    }

    if (existingPrompt.user_id !== userId) {
      console.error('User not authorized to delete this prompt.');
      return { success: false, error: new Error("Unauthorized") };
    }

    // Delete related data first (due to foreign key constraints)
    
    // Delete prompt tags
    const { error: tagsError } = await supabase
      .from('prompt_tags')
      .delete()
      .eq('prompt_id', promptId);
    
    if (tagsError) {
      console.error('Error deleting prompt tags:', tagsError.message);
      return { success: false, error: tagsError };
    }

    // Delete collection associations
    const { error: collectionsError } = await supabase
      .from('collection_prompts')
      .delete()
      .eq('prompt_id', promptId);
    
    if (collectionsError) {
      console.error('Error deleting collection associations:', collectionsError.message);
      return { success: false, error: collectionsError };
    }

    // Delete comments
    const { error: commentsError } = await supabase
      .from('comments')
      .delete()
      .eq('prompt_id', promptId);
    
    if (commentsError) {
      console.error('Error deleting comments:', commentsError.message);
      return { success: false, error: commentsError };
    }

    // Delete votes
    const { error: votesError } = await supabase
      .from('prompt_votes')
      .delete()
      .eq('prompt_id', promptId);
    
    if (votesError) {
      console.error('Error deleting votes:', votesError.message);
      return { success: false, error: votesError };
    }

    // Finally, delete the prompt itself
    const { error: deleteError } = await supabase
      .from('prompts')
      .delete()
      .eq('id', promptId)
      .eq('user_id', userId); // Double-check ownership
    
    if (deleteError) {
      console.error('Error deleting prompt:', deleteError.message);
      return { success: false, error: deleteError };
    }

    return { success: true };
  } catch (error: unknown) {
    console.error('Error in deletePrompt:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, error: errorMessage };
  }
}

/**
 * Get the count of public collections with prompts for the explore tab badge
 */
export async function getPublicCollectionsCount(): Promise<number> {
  try {
    const supabase = getSupabaseClientSafe();
    const { count, error } = await supabase
      .from("collections")
      .select("*", { count: "exact", head: true })
      .eq("is_public", true)
      .gt("prompt_count", 0);

    if (error) {
      console.error("Error fetching public collections count:", error.message);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error("Error in getPublicCollectionsCount:", error);
    return 0;
  }
}

/**
 * Get public collections for the explore page
 * Supports search, sorting, and pagination
 */
export async function getPublicCollections(options: {
  searchQuery?: string;
  sortBy?: "newest" | "popular" | "most_items";
  limit?: number;
  offset?: number;
} = {}): Promise<Collection[]> {
  const {
    searchQuery,
    sortBy = "newest",
    limit = 20,
    offset = 0,
  } = options;

  try {
    const supabase = getSupabaseClientSafe();
    let query = supabase
      .from("collections")
      .select(`
        id,
        user_id,
        name,
        description,
        icon,
        is_public,
        is_default,
        default_type,
        prompt_count,
        view_count,
        created_at,
        updated_at,
        user:profiles!collections_user_id_fkey (username, avatar_url)
      `)
      .eq("is_public", true)
      .gt("prompt_count", 0); // Filter out collections with 0 prompts

    // Apply search if provided
    if (searchQuery && searchQuery.trim()) {
      // Use the search_vector column if available, otherwise fall back to text search
      query = query.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
    }

    // Apply sorting
    switch (sortBy) {
      case "newest":
        query = query.order("created_at", { ascending: false });
        break;
      case "most_items":
        query = query.order("prompt_count", { ascending: false });
        break;
      case "popular":
        // For now, use created_at desc as placeholder for popularity
        // TODO: Implement proper popularity metrics (views, followers, etc.)
        query = query.order("created_at", { ascending: false });
        break;
      default:
        query = query.order("created_at", { ascending: false });
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error } = await query;

    if (error) {
      console.error("Error fetching public collections:", error.message);
      throw error;
    }

    return (data || []).map(transformCollection);
  } catch (error) {
    console.error("Error in getPublicCollections:", error);
    return [];
  }
}

/**
 * Universal search for typeahead - searches prompts, collections, and profiles
 * Uses the universal_search_typeahead RPC function from the database
 */
export async function universalSearchTypeahead(
  searchQuery: string,
  currentUserId?: string,
  limit: number = 7
): Promise<{
  id: string;
  title: string;
  itemType: "prompt" | "collection" | "profile";
  shortId?: string;
  slug?: string;
  userId: string;
  username: string;
  avatarUrl?: string;
  itemIcon?: string;
  rank: number;
}[]> {
  console.log(`[API] universalSearchTypeahead called with: "${searchQuery}", limit: ${limit}`);
  
  try {
    if (!searchQuery || searchQuery.trim().length === 0) {
      console.log(`[API] Empty query, returning empty array`);
      return [];
    }

    console.log(`[API] Making RPC call to universal_search_typeahead`);
    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase.rpc("universal_search_typeahead", {
      p_search_query: searchQuery.trim(),
      p_current_user_id: currentUserId || null,
      p_limit: limit,
    });

    console.log(`[API] RPC response - data:`, Array.isArray(data) ? data.length : 0, 'results, error:', error?.message || 'none');

    if (error) {
      console.error("Error in universal search typeahead:", error.message);
      console.log(`[API] Using fallback search`);
      
      // Fall back to separate searches for prompts, collections, and profiles
      const [promptResults, collectionResults] = await Promise.all([
        searchPrompts(searchQuery, { limit: Math.min(limit, 4) }),
        getPublicCollections({ 
          searchQuery, 
          limit: Math.min(limit, 3),
          sortBy: "newest" 
        })
      ]);
      
      // Simple profile search fallback
      const { data: profileData } = await supabase
        .from("profiles")
        .select("id, username, avatar_url")
        .ilike("username", `%${searchQuery}%`)
        .limit(Math.min(limit, 2));
      
      const combinedResults = [
        ...promptResults.map(prompt => ({
          id: prompt.id,
          title: prompt.title,
          itemType: "prompt" as const,
          shortId: prompt.shortId,
          slug: prompt.slug,
          userId: prompt.user.id,
          username: prompt.user.username,
          avatarUrl: prompt.user.avatarUrl || undefined,
          itemIcon: prompt.tool?.name,
          rank: 1,
        })),
        ...collectionResults
          .filter(collection => collection.promptCount && collection.promptCount > 0)
          .map(collection => ({
            id: collection.id,
            title: collection.name,
            itemType: "collection" as const,
            shortId: undefined,
            slug: undefined,
            userId: collection.userId,
            username: collection.user?.username || "unknown",
            avatarUrl: collection.user?.avatar_url || undefined,
            itemIcon: collection.icon || undefined,
            rank: 0.5,
          })),
        ...(profileData || []).map((profile: any) => ({
          id: profile.id as string,
          title: profile.username as string,
          itemType: "profile" as const,
          shortId: undefined,
          slug: profile.username as string,
          userId: profile.id as string,
          username: profile.username as string,
          avatarUrl: profile.avatar_url as string | undefined,
          itemIcon: undefined,
          rank: 0.3,
        }))
      ];
      
      console.log(`[API] Returning fallback results:`, combinedResults.length);
      return combinedResults.slice(0, limit);
    }

    const transformedResults = Array.isArray(data) ? data.map((item: any) => ({
      id: item.id,
      title: item.title,
      itemType: item.item_type as "prompt" | "collection" | "profile",
      shortId: item.short_id,
      slug: item.slug,
      userId: item.user_id,
      username: item.username,
      avatarUrl: item.avatar_url,
      itemIcon: item.item_icon,
      rank: item.rank,
    })) : [];

    console.log(`[API] Returning RPC results:`, transformedResults.length);
    return transformedResults;
  } catch (error) {
    console.error("Error in universalSearchTypeahead:", error);
    return [];
  }
}

export async function getOriginalPromptDetails(originalPromptId: string): Promise<{
  title: string;
  shortId: string;
  isPublic: boolean;
} | null> {
  try {
    console.log(`[api-services/getOriginalPromptDetails] Fetching original prompt details for ID: "${originalPromptId}"`);

    const supabase = getSupabaseClientSafe();
    const { data, error } = await supabase
      .from("prompts")
      .select("title, short_id, is_public")
      .eq("id", originalPromptId)
      .single();

    if (error) {
      console.error(`[api-services/getOriginalPromptDetails] Error fetching original prompt:`, error);
      return null;
    }

    if (!data) {
      console.warn(`[api-services/getOriginalPromptDetails] No data returned for original prompt ID: "${originalPromptId}"`);
      return null;
    }

    // Only return data if the prompt is public
    if (!data.is_public) {
      console.log(`[api-services/getOriginalPromptDetails] Original prompt is not public, returning null`);
      return null;
    }

    return {
      title: data.title as string,
      shortId: data.short_id as string,
      isPublic: data.is_public as boolean,
    };
  } catch (error) {
    console.error(`[api-services/getOriginalPromptDetails] Unexpected error:`, error);
    return null;
  }
}
