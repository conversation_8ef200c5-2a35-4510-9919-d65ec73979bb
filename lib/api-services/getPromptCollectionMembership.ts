import { supabase } from "../supabase/client";

/**
 * Gets the collection IDs that a prompt belongs to for a specific user
 * This is used to determine if a prompt is saved in any of the user's collections
 */
export async function getPromptCollectionMembership(
  userId: string,
  promptId: string
): Promise<{ collectionIds: string[]; error?: any }> {
  try {
    // Get all collections that belong to the user
    const { data: userCollections, error: collectionsError } = await supabase
      .from("collections")
      .select("id")
      .eq("user_id", userId);

    if (collectionsError) {
      console.error("Error fetching user collections:", collectionsError);
      return { collectionIds: [], error: collectionsError };
    }

    if (!userCollections || userCollections.length === 0) {
      // User has no collections
      return { collectionIds: [] };
    }

    // Get all collection_prompts entries for this prompt
    const userCollectionIds = userCollections.map((c: any) => c.id as string);
    
    const { data: memberships, error: membershipsError } = await supabase
      .from("collection_prompts")
      .select("collection_id")
      .eq("prompt_id", promptId)
      .in("collection_id", userCollectionIds);

    if (membershipsError) {
      console.error("Error fetching prompt collection memberships:", membershipsError);
      return { collectionIds: [], error: membershipsError };
    }

    // Return the collection IDs
    return { 
      collectionIds: memberships ? memberships.map((m: any) => m.collection_id as string) : [],
      error: null
    };
  } catch (e) {
    console.error("Unexpected error in getPromptCollectionMembership service:", e);
    return { collectionIds: [], error: e };
  }
}
